<template>
  <div
    class="expand-table-container"
    @wheel="handleWheel"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <VxeTable
      ref="vxeTableRef"
      :isShowTableHeader="false"
      :tableKey="tableKey"
      :columns="columns"
      :tableData="list"
      :loading="loading"
      :isShowSize="false"
      :isShowColumns="false"
      :isShowFull="false"
      :autoHeight="true"
      :rowConfig="{ isHover: false }"
      min-height="120px"
      max-height="240px"
      size="small"
      @refresh="getList"
    ></VxeTable>
    <OptCmdDetail v-if="showForm" ref="formRef" @ok="onOperationComplete" @close="showForm = false" />
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import OptCmdDetail from '../../opt-command/modules/OptCmdDetail.vue'
  import { getRunmdById } from '../services'
  import { getOperateCmdPage } from '../../opt-command/services'

  export default {
    name: 'ExpandTable',
    components: { VxeTable, OptCmdDetail },
    props: ['row'],
    data() {
      return {
        showForm: false,
        tableKey: 1,
        loading: true,
        list: [],
        selectIds: [],
        styleCheckInterval: null,
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '日期',
            field: 'operateDate',
            minWidth: 120,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                if (row.operateDate) {
                  // 处理日期格式，将 ISO 格式转换为 YYYY-MM-DD HH:mm:ss
                  const date = new Date(row.operateDate)
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false
                    }).replace(/\//g, '-')
                  }
                }
                return '-'
              }
            }
          },
          { title: '工程名称', field: 'projectName', minWidth: 120, showOverflow: 'tooltip' },
          { title: '操作人', field: 'operateName', minWidth: 100, showOverflow: 'tooltip' },
          { title: '监护人', field: 'guardianName', minWidth: 100, showOverflow: 'tooltip' },
          {
            title: '接收状态',
            field: 'recStatusCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                return row.recStatusCode === 1 ? '已接收' : '未接收'
              }
            }
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>详情</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {
      // 确保表格渲染完成后设置正确的滚动行为
      this.$nextTick(() => {
        this.setupScrollBehavior()
        // 延迟再次设置，确保其他组件初始化完成后样式仍然生效
        setTimeout(() => {
          this.setupScrollBehavior()
        }, 100)
        // 定期检查样式是否被覆盖
        this.styleCheckInterval = setInterval(() => {
          this.checkAndFixStyles()
        }, 1000)
      })
    },
    beforeDestroy() {
      if (this.styleCheckInterval) {
        clearInterval(this.styleCheckInterval)
      }
    },
    methods: {
      getList() {
        this.loading = true

        // 同时调用两个接口
        Promise.all([
          getRunmdById({ runCmdId: this.row.runCmdId }),
          getOperateCmdPage({ cmdCode: this.row.cmdCode, pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER })
        ]).then(([runmdResponse, operateResponse]) => {
          let projectOpInfoList = []
          let operateCmdList = []

          // 获取工程操作信息列表
          if (runmdResponse.code === 200 && runmdResponse.data && runmdResponse.data.projectOpInfoList) {
            projectOpInfoList = runmdResponse.data.projectOpInfoList
          }

          // 获取操作命令分页数据并倒序排列
          if (operateResponse.code === 200 && operateResponse.data && operateResponse.data.data) {
            operateCmdList = operateResponse.data.data.reverse() // 倒序排列
          }

          // 将operateCmdId赋值给projectOpInfoList中对应的项
          if (projectOpInfoList.length > 0 && operateCmdList.length > 0) {
            projectOpInfoList.forEach((item, index) => {
              if (operateCmdList[index] && operateCmdList[index].operateCmdId) {
                item.operateCmdId = operateCmdList[index].operateCmdId
              }
            })
          }

          this.list = projectOpInfoList
          this.loading = false
          // 数据加载完成后重新设置滚动行为
          this.$nextTick(() => {
            this.setupScrollBehavior()
          })
        }).catch(() => {
          this.list = []
          this.loading = false
          this.$nextTick(() => {
            this.setupScrollBehavior()
          })
        })
      },

      // 设置滚动行为
      setupScrollBehavior() {
        const tableBodyWrapper = this.$el.querySelector('.vxe-table--body-wrapper')
        if (tableBodyWrapper) {
          // 强制设置滚动容器样式，确保在生产环境中也能生效
          tableBodyWrapper.style.setProperty('overflow-y', 'auto', 'important')
          tableBodyWrapper.style.setProperty('overflow-x', 'hidden', 'important')
          tableBodyWrapper.style.setProperty('min-height', '120px', 'important')
          tableBodyWrapper.style.setProperty('max-height', '240px', 'important')
          tableBodyWrapper.style.setProperty('position', 'relative', 'important')

          // 强制重新计算滚动高度
          tableBodyWrapper.style.height = 'auto'
          this.$nextTick(() => {
            const computedHeight = tableBodyWrapper.scrollHeight
            if (computedHeight > 240) {
              tableBodyWrapper.style.setProperty('height', '240px', 'important')
            }
          })
        }

        // 同时设置表格容器的样式
        const vxeGrid = this.$el.querySelector('.vxe-grid')
        if (vxeGrid) {
          vxeGrid.style.setProperty('min-height', '120px', 'important')
          vxeGrid.style.setProperty('max-height', '240px', 'important')
        }

        const vxeTable = this.$el.querySelector('.vxe-table')
        if (vxeTable) {
          vxeTable.style.setProperty('min-height', '120px', 'important')
          vxeTable.style.setProperty('max-height', '240px', 'important')
        }

        // 设置容器本身的样式
        if (this.$el) {
          this.$el.style.setProperty('min-height', '120px', 'important')
          this.$el.style.setProperty('max-height', '240px', 'important')
        }

        // 触发VxeTable重新计算
        if (this.$refs.vxeTableRef && typeof this.$refs.vxeTableRef.recalculate === 'function') {
          this.$nextTick(() => {
            this.$refs.vxeTableRef.recalculate()
          })
        }
      },

      // 检查并修复样式
      checkAndFixStyles() {
        const tableBodyWrapper = this.$el.querySelector('.vxe-table--body-wrapper')
        if (tableBodyWrapper) {
          const computedStyle = window.getComputedStyle(tableBodyWrapper)
          // 检查关键样式是否正确
          if (computedStyle.maxHeight !== '240px' || computedStyle.overflowY !== 'auto') {
            this.setupScrollBehavior()
          }
        }
      },

      // 处理滚轮事件，智能处理滚动冒泡
      handleWheel(event) {
        // 查找表格的滚动容器
        const tableBodyWrapper = this.$el.querySelector('.vxe-table--body-wrapper')
        if (!tableBodyWrapper) {
          console.log('未找到滚动容器，让事件冒泡')
          return
        }

        const scrollTop = tableBodyWrapper.scrollTop
        const scrollHeight = tableBodyWrapper.scrollHeight
        const clientHeight = tableBodyWrapper.clientHeight
        const deltaY = event.deltaY

        // 添加调试信息
        console.log('滚动调试:', {
          scrollTop,
          scrollHeight,
          clientHeight,
          deltaY,
          needsScrollbar: scrollHeight > clientHeight + 2,
          atTop: scrollTop <= 0,
          atBottom: scrollTop >= scrollHeight - clientHeight
        })

        // 计算是否需要滚动条（考虑2px的误差）
        const needsScrollbar = scrollHeight > clientHeight + 2

        // 情况1：如果不需要滚动条，让事件继续冒泡到父级（整体页面滚动）
        if (!needsScrollbar) {
          console.log('不需要滚动条，让事件冒泡')
          return // 不阻止事件，让其自然冒泡
        }

        // 情况2：有滚动条的情况下，需要智能处理
        const atTop = scrollTop <= 0
        const atBottom = scrollTop >= scrollHeight - clientHeight

        // 如果在顶部且向上滚动，或在底部且向下滚动，则让父级处理
        if ((atTop && deltaY < 0) || (atBottom && deltaY > 0)) {
          console.log('到达边界，让父级处理滚动')
          // 不阻止事件，让其自然冒泡到父级
          return
        }

        // 其他情况：在表格内部滚动，阻止事件冒泡
        console.log('表格内部滚动')
        event.preventDefault()
        event.stopPropagation()

        // 执行表格内部滚动
        const newScrollTop = scrollTop + deltaY
        if (newScrollTop >= 0 && newScrollTop <= scrollHeight - clientHeight) {
          tableBodyWrapper.scrollTop = newScrollTop
        } else if (newScrollTop < 0) {
          tableBodyWrapper.scrollTop = 0
        } else {
          tableBodyWrapper.scrollTop = scrollHeight - clientHeight
        }
      },

      // 查找可滚动的父元素
      findScrollableParent(element) {
        let parent = element.parentElement
        while (parent && parent !== document.body) {
          const style = window.getComputedStyle(parent)
          const overflowY = style.overflowY
          const hasScrollbar = parent.scrollHeight > parent.clientHeight

          if ((overflowY === 'auto' || overflowY === 'scroll') && hasScrollbar) {
            return parent
          }
          parent = parent.parentElement
        }
        return document.documentElement || document.body
      },

      // 鼠标进入时添加焦点样式
      handleMouseEnter() {
        this.$el.classList.add('expand-table-focused')
      },

      // 鼠标离开时移除焦点样式
      handleMouseLeave() {
        this.$el.classList.remove('expand-table-focused')
      },

      handleDetail(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      onOperationComplete() {
        this.isShowModal = false
      },
    },
  }
</script>

<style lang="less" scoped>
  .expand-table-container {
    min-height: 120px !important;
    height: 240px !important;
    max-height: 240px !important;
    position: relative !important;
    overflow: hidden !important;
    border: 1px solid #e8e8e8 !important;
    border-radius: 4px !important;

    // 焦点状态样式
    &.expand-table-focused {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
    }
  }

  ::v-deep .vxe-table-box-content {
    .sortable-column-demo.vxe-grid {
      padding-bottom: 0px !important;
      margin-bottom: 0px !important;
    }
  }

  // 确保表格主体有正确的滚动行为 - 使用更高优先级的选择器
  .expand-table-container ::v-deep .vxe-table--body-wrapper,
  .expand-table-container ::v-deep .vxe-table--render-default .vxe-table--body-wrapper,
  .expand-table-container ::v-deep .vxe-grid .vxe-table--body-wrapper {
    min-height: 120px !important;
    max-height: 240px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    position: relative !important;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px !important;
    }

    &::-webkit-scrollbar-track {
      background: #f5f5f5 !important;
      border-radius: 3px !important;
    }

    &::-webkit-scrollbar-thumb {
      background: #d9d9d9 !important;
      border-radius: 3px !important;

      &:hover {
        background: #bfbfbf !important;
      }

      &:active {
        background: #999999 !important;
      }
    }
  }

  // 确保表格内容区域正确显示
  .expand-table-container ::v-deep .vxe-table--render-default .vxe-table--body,
  .expand-table-container ::v-deep .vxe-table--body {
    position: relative !important;
  }

  // 确保VxeTable组件本身的高度设置
  .expand-table-container ::v-deep .vxe-grid,
  .expand-table-container ::v-deep .vxe-table {
    min-height: 120px !important;
    max-height: 240px !important;
  }

  // 确保表格容器的高度设置
  .expand-table-container ::v-deep .vxe-table-box,
  .expand-table-container ::v-deep .vxe-table-box-content {
    min-height: 120px !important;
    max-height: 240px !important;
  }
</style>
