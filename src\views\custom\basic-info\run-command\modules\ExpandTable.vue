<template>
  <div
    class="expand-table-container"
    @wheel="handleWheel"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <VxeTable
      ref="vxeTableRef"
      :isShowTableHeader="false"
      :tableKey="tableKey"
      :columns="columns"
      :tableData="list"
      :loading="loading"
      :isShowSize="false"
      :isShowColumns="false"
      :isShowFull="false"
      :autoHeight="true"
      :rowConfig="{ isHover: false }"
      min-height="120px"
      max-height="240px"
      size="small"
      @refresh="getList"
    ></VxeTable>
    <OptCmdDetail v-if="showForm" ref="formRef" @ok="onOperationComplete" @close="showForm = false" />
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import OptCmdDetail from '../../opt-command/modules/OptCmdDetail.vue'
  import { getRunmdById } from '../services'
  import { getOperateCmdPage } from '../../opt-command/services'

  export default {
    name: 'ExpandTable',
    components: { VxeTable, OptCmdDetail },
    props: ['row'],
    data() {
      return {
        showForm: false,
        tableKey: 1,
        loading: true,
        list: [],
        selectIds: [],
        styleCheckInterval: null,
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '日期',
            field: 'operateDate',
            minWidth: 120,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                if (row.operateDate) {
                  // 处理日期格式，将 ISO 格式转换为 YYYY-MM-DD HH:mm:ss
                  const date = new Date(row.operateDate)
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false
                    }).replace(/\//g, '-')
                  }
                }
                return '-'
              }
            }
          },
          { title: '工程名称', field: 'projectName', minWidth: 120, showOverflow: 'tooltip' },
          { title: '操作人', field: 'operateName', minWidth: 100, showOverflow: 'tooltip' },
          { title: '监护人', field: 'guardianName', minWidth: 100, showOverflow: 'tooltip' },
          {
            title: '接收状态',
            field: 'recStatusCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                return row.recStatusCode === 1 ? '已接收' : '未接收'
              }
            }
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>详情</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {
      // 确保表格渲染完成后设置正确的滚动行为
      this.$nextTick(() => {
        this.setupScrollBehavior()
        // 延迟再次设置，确保其他组件初始化完成后样式仍然生效
        setTimeout(() => {
          this.setupScrollBehavior()
          this.debugScrollbar()
        }, 100)
        // 定期检查样式是否被覆盖
        this.styleCheckInterval = setInterval(() => {
          this.checkAndFixStyles()
        }, 1000)
      })
    },
    beforeDestroy() {
      if (this.styleCheckInterval) {
        clearInterval(this.styleCheckInterval)
      }
    },
    methods: {
      getList() {
        this.loading = true

        // 同时调用两个接口
        Promise.all([
          getRunmdById({ runCmdId: this.row.runCmdId }),
          getOperateCmdPage({ cmdCode: this.row.cmdCode, pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER })
        ]).then(([runmdResponse, operateResponse]) => {
          let projectOpInfoList = []
          let operateCmdList = []

          // 获取工程操作信息列表
          if (runmdResponse.code === 200 && runmdResponse.data && runmdResponse.data.projectOpInfoList) {
            projectOpInfoList = runmdResponse.data.projectOpInfoList
          }

          // 获取操作命令分页数据并倒序排列
          if (operateResponse.code === 200 && operateResponse.data && operateResponse.data.data) {
            operateCmdList = operateResponse.data.data.reverse() // 倒序排列
          }

          // 将operateCmdId赋值给projectOpInfoList中对应的项
          if (projectOpInfoList.length > 0 && operateCmdList.length > 0) {
            projectOpInfoList.forEach((item, index) => {
              if (operateCmdList[index] && operateCmdList[index].operateCmdId) {
                item.operateCmdId = operateCmdList[index].operateCmdId
              }
            })
          }

          this.list = projectOpInfoList
          this.loading = false
          // 数据加载完成后重新设置滚动行为
          this.$nextTick(() => {
            this.setupScrollBehavior()
          })
        }).catch(() => {
          this.list = []
          this.loading = false
          this.$nextTick(() => {
            this.setupScrollBehavior()
          })
        })
      },

      // 设置滚动行为
      setupScrollBehavior() {
        const tableBodyWrapper = this.$el.querySelector('.vxe-table--body-wrapper')
        if (tableBodyWrapper) {
          // 强制设置滚动容器样式，确保在生产环境中也能生效
          tableBodyWrapper.style.setProperty('overflow-y', 'auto', 'important')
          tableBodyWrapper.style.setProperty('overflow-x', 'hidden', 'important')
          tableBodyWrapper.style.setProperty('min-height', '120px', 'important')
          tableBodyWrapper.style.setProperty('max-height', '240px', 'important')
          tableBodyWrapper.style.setProperty('position', 'relative', 'important')
          // 确保滚动条可以接收鼠标事件
          tableBodyWrapper.style.setProperty('pointer-events', 'auto', 'important')
          tableBodyWrapper.style.setProperty('z-index', '1', 'important')

          // 移除可能阻止滚动条交互的属性
          tableBodyWrapper.style.removeProperty('user-select')
          tableBodyWrapper.style.removeProperty('-webkit-user-select')
          tableBodyWrapper.style.removeProperty('-moz-user-select')
          tableBodyWrapper.style.removeProperty('-ms-user-select')

          // 确保没有事件阻止
          tableBodyWrapper.style.setProperty('touch-action', 'auto', 'important')

          this.$nextTick(() => {
            // 检查是否需要强制显示滚动条
            const actualContentHeight = this.calculateActualContentHeight(tableBodyWrapper)
            console.log('实际内容高度计算:', actualContentHeight, '数据条数:', this.list.length)

            // 当数据条数超过4条时，强制显示滚动条（调整阈值）
            // 因为在240px高度限制下，5-7条数据通常需要滚动
            if (this.list.length > 4) {
              tableBodyWrapper.setAttribute('data-force-scroll', 'true')
              tableBodyWrapper.style.setProperty('height', '240px', 'important')
              tableBodyWrapper.style.setProperty('overflow-y', 'scroll', 'important') // 强制显示滚动条

              // 确保表格内容能够撑开高度 - 根据实际行数计算
              const estimatedContentHeight = Math.max(280, this.list.length * 35) // 每行约35px
              const tableBody = tableBodyWrapper.querySelector('tbody')
              if (tableBody) {
                tableBody.style.setProperty('min-height', `${estimatedContentHeight}px`, 'important')
              }

              console.log(`强制显示滚动条: 数据${this.list.length}条, 估算内容高度${estimatedContentHeight}px`)
            } else {
              tableBodyWrapper.removeAttribute('data-force-scroll')
              tableBodyWrapper.style.height = 'auto'
              tableBodyWrapper.style.setProperty('overflow-y', 'auto', 'important')

              // 移除强制高度
              const tableBody = tableBodyWrapper.querySelector('tbody')
              if (tableBody) {
                tableBody.style.removeProperty('min-height')
              }
            }

            // 强制触发滚动条重绘
            tableBodyWrapper.style.display = 'none'
            tableBodyWrapper.offsetHeight // 触发重排
            tableBodyWrapper.style.display = 'block'
          })
        }

        // 同时设置表格容器的样式
        const vxeGrid = this.$el.querySelector('.vxe-grid')
        if (vxeGrid) {
          vxeGrid.style.setProperty('min-height', '120px', 'important')
          vxeGrid.style.setProperty('max-height', '240px', 'important')
        }

        const vxeTable = this.$el.querySelector('.vxe-table')
        if (vxeTable) {
          vxeTable.style.setProperty('min-height', '120px', 'important')
          vxeTable.style.setProperty('max-height', '240px', 'important')
        }

        // 设置容器本身的样式
        if (this.$el) {
          this.$el.style.setProperty('min-height', '120px', 'important')
          this.$el.style.setProperty('max-height', '240px', 'important')
        }

        // 触发VxeTable重新计算
        if (this.$refs.vxeTableRef) {
          this.$nextTick(() => {
            this.$refs.vxeTableRef.recalculate()
          })
        }
      },

      // 检查并修复样式
      checkAndFixStyles() {
        const tableBodyWrapper = this.$el.querySelector('.vxe-table--body-wrapper')
        if (tableBodyWrapper) {
          const computedStyle = window.getComputedStyle(tableBodyWrapper)
          // 检查关键样式是否正确
          if (computedStyle.maxHeight !== '240px' || computedStyle.overflowY !== 'auto') {
            this.setupScrollBehavior()
          }
        }
      },

      // 计算实际内容高度
      calculateActualContentHeight(tableBodyWrapper) {
        if (!tableBodyWrapper) return 0

        // 临时移除高度限制来计算真实内容高度
        const originalHeight = tableBodyWrapper.style.height
        const originalMaxHeight = tableBodyWrapper.style.maxHeight

        tableBodyWrapper.style.height = 'auto'
        tableBodyWrapper.style.maxHeight = 'none'

        const actualHeight = tableBodyWrapper.scrollHeight

        // 恢复原始样式
        tableBodyWrapper.style.height = originalHeight
        tableBodyWrapper.style.maxHeight = originalMaxHeight

        return actualHeight
      },

      // 调试滚动条状态
      debugScrollbar() {
        const tableBodyWrapper = this.$el.querySelector('.vxe-table--body-wrapper')
        if (tableBodyWrapper) {
          const computedStyle = window.getComputedStyle(tableBodyWrapper)
          console.log('滚动条调试信息:', {
            scrollHeight: tableBodyWrapper.scrollHeight,
            clientHeight: tableBodyWrapper.clientHeight,
            offsetWidth: tableBodyWrapper.offsetWidth,
            clientWidth: tableBodyWrapper.clientWidth,
            scrollbarWidth: tableBodyWrapper.offsetWidth - tableBodyWrapper.clientWidth,
            overflowY: computedStyle.overflowY,
            pointerEvents: computedStyle.pointerEvents,
            zIndex: computedStyle.zIndex,
            position: computedStyle.position
          })

          // 检查是否有其他元素覆盖滚动条
          const rect = tableBodyWrapper.getBoundingClientRect()
          const scrollbarArea = {
            left: rect.right - 15, // 滚动条区域
            top: rect.top,
            right: rect.right,
            bottom: rect.bottom
          }

          console.log('滚动条区域:', scrollbarArea)

          // 检查滚动条区域的元素
          const elementAtScrollbar = document.elementFromPoint(
            scrollbarArea.left + 5,
            scrollbarArea.top + 50
          )
          console.log('滚动条位置的元素:', elementAtScrollbar)
        }
      },

      // 处理滚轮事件，智能处理滚动冒泡
      handleWheel(event) {
        // 尝试查找多个可能的滚动容器
        const selectors = [
          '.vxe-table--body-wrapper',
          '.vxe-table--render-default .vxe-table--body-wrapper',
          '.vxe-grid .vxe-table--body-wrapper'
        ]

        let tableBodyWrapper = null
        for (const selector of selectors) {
          tableBodyWrapper = this.$el.querySelector(selector)
          if (tableBodyWrapper) break
        }

        if (!tableBodyWrapper) {
          return
        }

        const scrollTop = tableBodyWrapper.scrollTop
        const scrollHeight = tableBodyWrapper.scrollHeight
        const clientHeight = tableBodyWrapper.clientHeight
        const deltaY = event.deltaY

        // 详细的调试信息（生产环境可注释掉）
        // console.log('Scroll Debug:', {
        //   selector: selectors.find(s => this.$el.querySelector(s) === tableBodyWrapper),
        //   scrollTop,
        //   scrollHeight,
        //   clientHeight,
        //   deltaY,
        //   needsScrollbar: scrollHeight > clientHeight,
        //   hasOverflow: scrollHeight > clientHeight + 5,
        //   atTop: scrollTop <= 0,
        //   atBottom: scrollTop >= scrollHeight - clientHeight,
        //   computedStyle: {
        //     overflowY: window.getComputedStyle(tableBodyWrapper).overflowY,
        //     maxHeight: window.getComputedStyle(tableBodyWrapper).maxHeight,
        //     height: window.getComputedStyle(tableBodyWrapper).height
        //   }
        // })

        // 检查表格是否真的有内容需要滚动
        const tableRows = tableBodyWrapper.querySelectorAll('tr')
        const tableBody = tableBodyWrapper.querySelector('tbody')

        // 计算实际内容高度
        let actualContentHeight = 0
        if (tableBody) {
          actualContentHeight = tableBody.scrollHeight || tableBody.offsetHeight
        }

        // console.log('表格行数:', tableRows.length, '数据条数:', this.list.length)
        // console.log('实际内容高度:', actualContentHeight, '容器高度:', clientHeight)

        // 根据数据条数和行高估算是否需要滚动
        // 假设每行高度约28px（包括边距），7行应该约196px，但在240px限制内可能需要滚动
        const estimatedHeight = this.list.length * 28 // 估算高度
        const shouldHaveScroll = estimatedHeight > 240 || actualContentHeight > clientHeight || this.list.length > 6

        // console.log('估算高度:', estimatedHeight, '是否应该有滚动:', shouldHaveScroll)

        // 如果数据很少，确实不需要内部滚动
        if (this.list.length <= 2) {
          return
        }

        // 使用更智能的判断：主要基于数据条数，因为DOM属性可能不准确
        // 当数据超过6条时，在240px高度限制下肯定需要滚动
        const hasOverflow = this.list.length > 6 || scrollHeight > clientHeight || shouldHaveScroll

        // 情况1：如果确实不需要滚动，让事件冒泡到父级
        if (!hasOverflow) {
          return
        }

        // 情况2：有溢出内容，需要智能处理滚动
        event.preventDefault()
        event.stopPropagation()

        // 检查是否到达边界
        const atTop = scrollTop <= 0
        const atBottom = scrollTop >= scrollHeight - clientHeight

        // 如果在边界且继续向边界方向滚动，则手动触发父级滚动
        if ((atTop && deltaY < 0) || (atBottom && deltaY > 0)) {
          const parentScrollable = this.findScrollableParent(this.$el)
          if (parentScrollable) {
            parentScrollable.scrollTop += deltaY
          }
          return
        }

        // 在表格内部正常滚动
        const newScrollTop = scrollTop + deltaY
        if (newScrollTop >= 0 && newScrollTop <= scrollHeight - clientHeight) {
          tableBodyWrapper.scrollTop = newScrollTop
        } else if (newScrollTop < 0) {
          tableBodyWrapper.scrollTop = 0
        } else {
          tableBodyWrapper.scrollTop = scrollHeight - clientHeight
        }
      },

      // 检查是否点击在滚动条区域
      isClickOnScrollbar(event, element) {
        // 查找最近的有滚动条的元素
        let scrollableElement = element
        while (scrollableElement && scrollableElement !== this.$el) {
          const style = window.getComputedStyle(scrollableElement)
          if (style.overflowY === 'auto' || style.overflowY === 'scroll') {
            const rect = scrollableElement.getBoundingClientRect()
            const scrollbarWidth = scrollableElement.offsetWidth - scrollableElement.clientWidth

            // 检查鼠标是否在滚动条区域（右侧）
            if (scrollbarWidth > 0 && event.clientX > rect.right - scrollbarWidth) {
              return true
            }
            break
          }
          scrollableElement = scrollableElement.parentElement
        }
        return false
      },

      // 查找可滚动的父元素
      findScrollableParent(element) {
        let parent = element.parentElement
        while (parent && parent !== document.body) {
          const style = window.getComputedStyle(parent)
          const overflowY = style.overflowY
          const hasScrollbar = parent.scrollHeight > parent.clientHeight

          if ((overflowY === 'auto' || overflowY === 'scroll') && hasScrollbar) {
            return parent
          }
          parent = parent.parentElement
        }
        return document.documentElement || document.body
      },



      // 鼠标进入时添加焦点样式
      handleMouseEnter() {
        this.$el.classList.add('expand-table-focused')
      },

      // 鼠标离开时移除焦点样式
      handleMouseLeave() {
        this.$el.classList.remove('expand-table-focused')
      },

      handleDetail(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      onOperationComplete() {
        this.isShowModal = false
      },
    },
  }
</script>

<style lang="less" scoped>
  .expand-table-container {
    min-height: 120px !important;
    height: 240px !important;
    max-height: 240px !important;
    position: relative !important;
    overflow: visible !important; // 改为visible，避免隐藏滚动条
    border: 1px solid #e8e8e8 !important;
    border-radius: 4px !important;

    // 焦点状态样式
    &.expand-table-focused {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
    }
  }

  ::v-deep .vxe-table-box-content {
    .sortable-column-demo.vxe-grid {
      padding-bottom: 0px !important;
      margin-bottom: 0px !important;
    }
  }

  // 确保表格主体有正确的滚动行为 - 使用更高优先级的选择器
  .expand-table-container ::v-deep .vxe-table--body-wrapper,
  .expand-table-container ::v-deep .vxe-table--render-default .vxe-table--body-wrapper,
  .expand-table-container ::v-deep .vxe-grid .vxe-table--body-wrapper {
    min-height: 120px !important;
    max-height: 240px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    position: relative !important;
    // 确保滚动容器可以接收所有鼠标事件
    pointer-events: auto !important;
    // 确保滚动条在最上层
    z-index: 1 !important;

    // 当内容超过6行时，强制显示滚动条
    &[data-force-scroll="true"] {
      overflow-y: scroll !important;
      height: 240px !important;
    }

    // 自定义滚动条样式 - 确保可以正常拖动
    &::-webkit-scrollbar {
      width: 12px !important; // 增加宽度，便于拖动
      height: 12px !important;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1 !important;
      border-radius: 6px !important;
      // 确保轨道可以接收鼠标事件
      pointer-events: auto !important;
      // 添加边框，使滚动条更明显
      border: 1px solid #e0e0e0 !important;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1 !important;
      border-radius: 6px !important;
      // 确保滚动条滑块可以接收鼠标事件
      pointer-events: auto !important;
      // 添加最小高度，确保有足够的拖动区域
      min-height: 30px !important;
      // 添加边框，使滚动条更立体
      border: 1px solid #b0b0b0 !important;

      &:hover {
        background: #a8a8a8 !important;
        cursor: pointer !important;
        border-color: #999999 !important;
      }

      &:active {
        background: #888888 !important;
        cursor: grabbing !important;
        border-color: #777777 !important;
      }
    }

    // 确保滚动条区域不被其他元素遮挡
    &::-webkit-scrollbar-corner {
      background: #f1f1f1 !important;
    }
  }

  // 确保表格内容区域正确显示
  .expand-table-container ::v-deep .vxe-table--render-default .vxe-table--body,
  .expand-table-container ::v-deep .vxe-table--body {
    position: relative !important;
  }

  // 确保VxeTable组件本身的高度设置
  .expand-table-container ::v-deep .vxe-grid,
  .expand-table-container ::v-deep .vxe-table {
    min-height: 120px !important;
    max-height: 240px !important;
  }

  // 确保表格容器的高度设置
  .expand-table-container ::v-deep .vxe-table-box,
  .expand-table-container ::v-deep .vxe-table-box-content {
    min-height: 120px !important;
    max-height: 240px !important;
  }
</style>
